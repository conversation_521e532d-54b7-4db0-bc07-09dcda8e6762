use blake2::{Blake2s256, Digest};
use chacha20poly1305::{ChaCha20Poly1305, Key<PERSON>ni<PERSON>, aead::Aead};
use dialoguer::{Input, Password};
use rand::Rng;
use zeroize::Zeroize;

fn derive_key(password: &str, nonce: &[u8]) -> [u8; 32] {
    let mut hasher = Blake2s256::default();
    hasher.update(password.as_bytes());
    hasher.update(nonce);
    hasher.finalize().into()
}

pub(crate) fn encrypt() -> Result<(), Box<dyn std::error::Error>> {
    let input: String = Input::new().with_prompt("input").interact_text()?;
    let mut password = Password::new()
        .with_prompt("secret")
        .with_confirmation("confirm", "mismatch")
        .interact()?;

    // generate random nonce
    let mut rng = rand::rng();
    let nonce: [u8; 12] = rng.random();

    let key = derive_key(&password, &nonce);
    let cipher = ChaCha20Poly1305::new(&key.into());

    let ciphertext = cipher
        .encrypt(&nonce.into(), input.as_bytes())
        .map_err(|_| "encryption failed")?;

    // format: nonce (12) + ciphertext
    let mut data = Vec::new();
    data.extend_from_slice(&nonce);
    data.extend_from_slice(&ciphertext);

    password.zeroize();
    println!("{}", hex::encode(data));
    Ok(())
}

pub(crate) fn decrypt() -> Result<(), Box<dyn std::error::Error>> {
    let input: String = Input::new().with_prompt("input").interact_text()?;
    let mut password = Password::new().with_prompt("secret").interact()?;

    let data = hex::decode(input)?;

    // Extract nonce (12) + ciphertext
    let nonce = &data[..12];
    let ciphertext = &data[12..];

    let key = derive_key(&password, nonce);
    let cipher = ChaCha20Poly1305::new(&key.into());

    let nonce_array: [u8; 12] = nonce.try_into().unwrap();
    let plaintext = cipher
        .decrypt(&nonce_array.into(), ciphertext)
        .map_err(|_| "decryption failed")?;

    password.zeroize();
    println!("{}", String::from_utf8(plaintext)?);
    Ok(())
}
