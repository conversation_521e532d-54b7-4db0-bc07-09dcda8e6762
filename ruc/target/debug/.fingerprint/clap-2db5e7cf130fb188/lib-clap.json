{"rustc": 15597765236515928571, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15599109589607159429, "path": 16348228996857512912, "deps": [[1457576002496728321, "clap_derive", false, 16965406651349730850], [12790452388100355468, "clap_builder", false, 15287886717057459518]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-2db5e7cf130fb188/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}