{"$message_type":"diagnostic","message":"this function takes 2 arguments but 3 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/crypto.rs","byte_start":889,"byte_end":895,"line_start":28,"line_end":28,"column_start":44,"column_end":50,"is_primary":false,"text":[{"text":"    let key = derive_key(&password, &salt, &nonce);","highlight_start":44,"highlight_end":50}],"label":"unexpected argument #3 of type `&[u8; 12]`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/crypto.rs","byte_start":860,"byte_end":870,"line_start":28,"line_end":28,"column_start":15,"column_end":25,"is_primary":true,"text":[{"text":"    let key = derive_key(&password, &salt, &nonce);","highlight_start":15,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"function defined here","code":null,"level":"note","spans":[{"file_name":"src/crypto.rs","byte_start":172,"byte_end":182,"line_start":7,"line_end":7,"column_start":4,"column_end":14,"is_primary":true,"text":[{"text":"fn derive_key(password: &str, nonce: &[u8]) -> [u8; 32] {","highlight_start":4,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra argument","code":null,"level":"help","spans":[{"file_name":"src/crypto.rs","byte_start":887,"byte_end":895,"line_start":28,"line_end":28,"column_start":42,"column_end":50,"is_primary":true,"text":[{"text":"    let key = derive_key(&password, &salt, &nonce);","highlight_start":42,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 2 arguments but 3 arguments were supplied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/crypto.rs:28:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let key = derive_key(&password, &salt, &nonce);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `&[u8; 12]`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: function defined here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/crypto.rs:7:4\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn derive_key(password: &str, nonce: &[u8]) -> [u8; 32] {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let key = derive_key(&password, &salt\u001b[0m\u001b[0m\u001b[38;5;9m, &nonce\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let key = derive_key(&password, &salt);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 2 arguments but 3 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/crypto.rs","byte_start":1790,"byte_end":1795,"line_start":57,"line_end":57,"column_start":43,"column_end":48,"is_primary":false,"text":[{"text":"    let key = derive_key(&password, salt, nonce);","highlight_start":43,"highlight_end":48}],"label":"unexpected argument #3 of type `&[u8]`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/crypto.rs","byte_start":1762,"byte_end":1772,"line_start":57,"line_end":57,"column_start":15,"column_end":25,"is_primary":true,"text":[{"text":"    let key = derive_key(&password, salt, nonce);","highlight_start":15,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"function defined here","code":null,"level":"note","spans":[{"file_name":"src/crypto.rs","byte_start":172,"byte_end":182,"line_start":7,"line_end":7,"column_start":4,"column_end":14,"is_primary":true,"text":[{"text":"fn derive_key(password: &str, nonce: &[u8]) -> [u8; 32] {","highlight_start":4,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra argument","code":null,"level":"help","spans":[{"file_name":"src/crypto.rs","byte_start":1788,"byte_end":1795,"line_start":57,"line_end":57,"column_start":41,"column_end":48,"is_primary":true,"text":[{"text":"    let key = derive_key(&password, salt, nonce);","highlight_start":41,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 2 arguments but 3 arguments were supplied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/crypto.rs:57:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let key = derive_key(&password, salt, nonce);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `&[u8]`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: function defined here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/crypto.rs:7:4\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn derive_key(password: &str, nonce: &[u8]) -> [u8; 32] {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let key = derive_key(&password, salt\u001b[0m\u001b[0m\u001b[38;5;9m, nonce\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let key = derive_key(&password, salt);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0061`.\u001b[0m\n"}
