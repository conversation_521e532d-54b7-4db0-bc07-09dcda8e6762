{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 9298903534527576498, "path": 872911591461175784, "deps": [[7896293946984509699, "bitflags", false, 3971006093318983508], [10004434995811528692, "build_script_build", false, 6016355448729901724], [12846346674781677812, "linux_raw_sys", false, 8500397321320861624]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-13997acea986ebdb/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}