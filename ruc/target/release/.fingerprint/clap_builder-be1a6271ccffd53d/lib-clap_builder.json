{"rustc": 15597765236515928571, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 16545168011660328167, "deps": [[5820056977320921005, "anstream", false, 9017240745838741626], [9394696648929125047, "anstyle", false, 8246129336492371265], [11166530783118767604, "strsim", false, 6471670381916341052], [11649982696571033535, "clap_lex", false, 1563113357360273274]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-be1a6271ccffd53d/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}