{"rustc": 15597765236515928571, "features": "[\"color\", \"default\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9656904095642909417, "path": 16348228996857512912, "deps": [[12790452388100355468, "clap_builder", false, 17094365606838005478]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-85966d562d147f47/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}