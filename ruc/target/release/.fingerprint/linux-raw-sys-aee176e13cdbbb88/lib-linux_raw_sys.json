{"rustc": 15597765236515928571, "features": "[\"elf\", \"errno\", \"general\", \"ioctl\", \"no_std\"]", "declared_features": "[\"bootparam\", \"btrfs\", \"compiler_builtins\", \"core\", \"default\", \"elf\", \"elf_uapi\", \"errno\", \"general\", \"if_arp\", \"if_ether\", \"if_packet\", \"image\", \"io_uring\", \"ioctl\", \"landlock\", \"loop_device\", \"mempolicy\", \"net\", \"netlink\", \"no_std\", \"prctl\", \"ptrace\", \"rustc-dep-of-std\", \"std\", \"system\", \"xdp\"]", "target": 5772965225213482929, "profile": 4314370921045452772, "path": 10895930235177289687, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/linux-raw-sys-aee176e13cdbbb88/dep-lib-linux_raw_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}