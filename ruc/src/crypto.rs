use blake2::{Blake2b512, Digest};
use chacha20poly1305::{ChaCha20Poly1305, KeyIni<PERSON>, aead::Aead};
use dialoguer::{Input, Password};
use rand::Rng;
use zeroize::{Zeroize, ZeroizeOnDrop};

fn derive_key(password: &str, salt: &[u8], nonce: &[u8]) -> [u8; 32] {
    let mut hasher = Blake2b512::default();
    hasher.update(password.as_bytes());
    hasher.update(salt);
    hasher.update(nonce);
    let hash = hasher.finalize();

    let mut key = [0u8; 32];
    key.copy_from_slice(&hash[..32]);
    key
}

pub(crate) fn encrypt() -> Result<(), Box<dyn std::error::Error>> {
    let input: String = Input::new().with_prompt("input").interact_text()?;
    let mut password = Password::new()
        .with_prompt("secret")
        .with_confirmation("confirm", "mismatch")
        .interact()?;

    // Generate random salt and nonce
    let mut rng = rand::rng();
    let salt: [u8; 16] = rng.random();
    let nonce: [u8; 12] = rng.random();

    let key = derive_key(&password, &salt, &nonce);
    let cipher = ChaCha20Poly1305::new(&key.into());

    let ciphertext = cipher
        .encrypt(&nonce.into(), input.as_bytes())
        .map_err(|_| "encryption failed")?;

    // Format: salt (16) + nonce (12) + ciphertext
    let mut data = Vec::new();
    data.extend_from_slice(&salt);
    data.extend_from_slice(&nonce);
    data.extend_from_slice(&ciphertext);

    password.zeroize();
    println!("{}", hex::encode(data));
    Ok(())
}

pub(crate) fn decrypt() -> Result<(), Box<dyn std::error::Error>> {
    let input: String = Input::new().with_prompt("input").interact_text()?;
    let mut password = Password::new().with_prompt("secret").interact()?;

    let data = hex::decode(input)?;

    // Extract salt (16) + nonce (12) + ciphertext
    let salt = &data[..16];
    let nonce = &data[16..28];
    let ciphertext = &data[28..];

    let key = derive_key(&password, salt, nonce);
    let cipher = ChaCha20Poly1305::new(&key.into());

    let nonce_array: [u8; 12] = nonce.try_into().unwrap();
    let plaintext = cipher
        .decrypt(&nonce_array.into(), ciphertext)
        .map_err(|_| "decryption failed")?;

    password.zeroize();
    println!("{}", String::from_utf8(plaintext)?);
    Ok(())
}
