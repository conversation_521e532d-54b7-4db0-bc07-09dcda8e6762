use blake2::{<PERSON>2b512, <PERSON>2s256, Digest};
use chacha20poly1305::{ChaCha20Poly1305, KeyInit, aead::Aead};
use dialoguer::{Input, Password};
use zeroize::{Zeroize, ZeroizeOnDrop};

#[derive(ZeroizeOnDrop)]
struct Keys {
    secret: [u8; 32],
    nonce: [u8; 12],
    salt: [u8; 16],
}

fn derive_keys(password: &str) -> Keys {
    let mut hasher = Blake2s256::default();
    hasher.update(password.as_bytes());
    let hash: blake2::digest::generic_array::GenericArray<u8, blake2::digest::typenum::UInt<blake2::digest::typenum::UInt<blake2::digest::typenum::UInt<blake2::digest::typenum::UInt<blake2::digest::typenum::UInt<blake2::digest::typenum::UInt<blake2::digest::typenum::UTerm, chacha20poly1305::consts::B1>, chacha20poly1305::consts::B0>, chacha20poly1305::consts::B0>, chacha20poly1305::consts::B0>, chacha20poly1305::consts::B0>, chacha20poly1305::consts::B0>>s = hasher.finalize();

    let mut secret = [0u8; 32];
    let mut nonce = [0u8; 12];
    let mut salt = [0u8; 16];

    // secret.copy_from_slice(&hash[..32]);
    // nonce.copy_from_slice(&hash[32..44]);
    // salt.copy_from_slice(&hash[44..60]);

    Keys {
        secret,
        nonce,
        salt,
    }
}

pub(crate) fn encrypt() -> Result<(), Box<dyn std::error::Error>> {
    let input: String = Input::new().with_prompt("input").interact_text()?;
    let mut password = Password::new()
        .with_prompt("secret")
        .with_confirmation("confirm", "mismatch")
        .interact()?;

    let keys = derive_keys(&password);
    let cipher = ChaCha20Poly1305::new(&keys.secret.into());

    let ciphertext = cipher
        .encrypt(&keys.nonce.into(), input.as_bytes())
        .map_err(|_| "encryption failed")?;

    let mut data = Vec::new();
    data.extend_from_slice(&keys.salt);
    data.extend_from_slice(&ciphertext);

    password.zeroize();
    println!("{}", hex::encode(data));
    Ok(())
}

pub(crate) fn decrypt() -> Result<(), Box<dyn std::error::Error>> {
    let input: String = Input::new().with_prompt("input").interact_text()?;
    let mut password = Password::new().with_prompt("secret").interact()?;

    let data = hex::decode(input)?;
    let ciphertext = &data[16..];

    let keys = derive_keys(&password);
    let cipher = ChaCha20Poly1305::new(&keys.secret.into());

    let plaintext = cipher
        .decrypt(&keys.nonce.into(), ciphertext)
        .map_err(|_| "decryption failed")?;

    password.zeroize();
    println!("{}", String::from_utf8(plaintext)?);
    Ok(())
}
