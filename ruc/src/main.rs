use blake2::{Blake2b512, Digest};
use chacha20poly1305::{ChaCha20Poly1305, KeyInit, aead::Aead};
use dialoguer::{Input, Password};
use std::io::stdin;

pub fn derive_key_nonce_and_salt(password: &str) -> ([u8; 32], [u8; 12], [u8; 16]) {
    let mut hasher = Blake2b512::default();
    hasher.update(password.as_bytes());
    let hash = hasher.finalize();

    let mut key = [0u8; 32];
    let mut nonce = [0u8; 12];
    let mut salt = [0u8; 16];

    key.copy_from_slice(&hash[..32]);
    nonce.copy_from_slice(&hash[32..44]);
    salt.copy_from_slice(&hash[44..60]);

    (key, nonce, salt)
}

pub fn encrypt(plaintext: &str, password: &str) -> Result<String, Box<dyn std::error::Error>> {
    let (key, nonce, salt) = derive_key_nonce_and_salt(password);
    let cipher = ChaCha20Poly1305::new(&key.into());

    let ciphertext = cipher.encrypt(&nonce.into(), plaintext.as_bytes()).unwrap();

    // Format: salt (16 bytes) + ciphertext
    let mut combined = Vec::new();
    combined.extend_from_slice(&salt);
    combined.extend_from_slice(&ciphertext);

    Ok(hex::encode(combined))
}

pub fn decrypt(encrypted_hex: &str, password: &str) -> Result<String, Box<dyn std::error::Error>> {
    let combined = hex::decode(encrypted_hex)?;

    if combined.len() < 16 {
        return Err("Invalid encrypted data".into());
    }

    let ciphertext = &combined[16..];

    let (key, nonce, _) = derive_key_nonce_and_salt(password);
    let cipher = ChaCha20Poly1305::new(&key.into());

    let plaintext = cipher
        .decrypt(&nonce.into(), ciphertext)
        .expect("failed to decrypt");

    Ok(String::from_utf8(plaintext)?)
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Enter text to encrypt:");
    let input: String = Input::new()
        .with_prompt("Enter text to encrypt:")
        .interact_text()
        .unwrap();

    let password = Password::new()
        .with_prompt("New Password")
        .with_confirmation("Confirm password", "Passwords mismatching")
        .interact()
        .unwrap();

    // Encrypt
    let encrypted_hex = encrypt(&input, &password)?;
    println!("Encrypted: {}", encrypted_hex);

    // Decrypt to verify
    let decrypted = decrypt(&encrypted_hex, &password)?;
    println!("Decrypted: {}", decrypted);

    Ok(())
}
