use blake2::{Blake2b512, Digest};
use chacha20poly1305::{ChaCha20Poly1305, KeyInit, aead::Aead};
use clap::{Parser, Subcommand};
use dialoguer::{Input, Password};
use zeroize::{Zeroize, ZeroizeOnDrop};

#[derive(Parser)]
#[command(name = "ruc")]
#[command(about = "A CPB tool")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Encrypt data
    Encrypt,
    /// Decrypt data
    Decrypt,
}

#[derive(ZeroizeOnDrop)]
struct Keys {
    key: [u8; 32],
    nonce: [u8; 12],
    salt: [u8; 16],
}

fn derive_keys(password: &str) -> Keys {
    let mut hasher = Blake2b512::default();
    hasher.update(password.as_bytes());
    let hash = hasher.finalize();

    let mut key = [0u8; 32];
    let mut nonce = [0u8; 12];
    let mut salt = [0u8; 16];

    key.copy_from_slice(&hash[..32]);
    nonce.copy_from_slice(&hash[32..44]);
    salt.copy_from_slice(&hash[44..60]);

    Keys { key, nonce, salt }
}

fn encrypt(plaintext: &str, password: &str) -> Result<String, Box<dyn std::error::Error>> {
    let keys = derive_keys(password);
    let cipher = ChaCha20Poly1305::new(&keys.key.into());

    let ciphertext = cipher
        .encrypt(&keys.nonce.into(), plaintext.as_bytes())
        .map_err(|e| format!("encrypt failed: {}", e))?;

    let mut data = Vec::new();
    data.extend_from_slice(&keys.salt);
    data.extend_from_slice(&ciphertext);

    Ok(hex::encode(data))
}

fn decrypt(encrypted_hex: &str, password: &str) -> Result<String, Box<dyn std::error::Error>> {
    let data = hex::decode(encrypted_hex)?;
    let ciphertext = &data[16..];

    let keys = derive_keys(password);
    let cipher = ChaCha20Poly1305::new(&keys.key.into());

    let plaintext = cipher
        .decrypt(&keys.nonce.into(), ciphertext)
        .expect("decrypt failed");

    Ok(String::from_utf8(plaintext)?)
}

fn run_encrypt() -> Result<(), Box<dyn std::error::Error>> {
    let mut password = Password::new()
        .with_prompt("password")
        .with_confirmation("confirm", "mismatch")
        .interact()?;

    let input: String = Input::new().with_prompt("plaintext").interact_text()?;

    let encrypted = encrypt(&input, &password)?;
    password.zeroize();

    println!("{}", encrypted);
    Ok(())
}

fn run_decrypt() -> Result<(), Box<dyn std::error::Error>> {
    let mut password = Password::new().with_prompt("password").interact()?;
    let input: String = Input::new().with_prompt("ciphertext").interact_text()?;

    let decrypted = decrypt(&input, &password)?;
    password.zeroize();

    println!("{}", decrypted);
    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let cli = Cli::parse();

    match cli.command {
        Commands::Encrypt => run_encrypt(),
        Commands::Decrypt => run_decrypt(),
    }
}
