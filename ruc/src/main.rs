use blake2::{Blake2b512, Digest};
use chacha20poly1305::{ChaCha20Poly1305, KeyInit, aead::Aead};
use clap::{Parser, Subcommand};
use dialoguer::{Input, Password};
use zeroize::{Zeroize, ZeroizeOnDrop};

#[derive(Parser)]
#[command(name = "ruc")]
#[command(about = "A CPB tool")]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,
}

#[derive(Subcommand)]
enum Commands {
    /// Decrypt data (default is encrypt)
    Decrypt,
}

#[derive(ZeroizeOnDrop)]
struct CryptoMaterial {
    key: [u8; 32],
    nonce: [u8; 12],
    salt: [u8; 16],
}

impl CryptoMaterial {
    fn from_password(password: &str) -> Self {
        let mut hasher = Blake2b512::default();
        hasher.update(password.as_bytes());
        let hash = hasher.finalize();

        let mut key = [0u8; 32];
        let mut nonce = [0u8; 12];
        let mut salt = [0u8; 16];

        key.copy_from_slice(&hash[..32]);
        nonce.copy_from_slice(&hash[32..44]);
        salt.copy_from_slice(&hash[44..60]);

        Self { key, nonce, salt }
    }
}

struct Encryptor {
    cipher: ChaCha20Poly1305,
    material: CryptoMaterial,
}

impl Encryptor {
    fn new(password: &str) -> Self {
        let material = CryptoMaterial::from_password(password);
        let cipher = ChaCha20Poly1305::new(&material.key.into());
        Self { cipher, material }
    }

    fn encrypt(&self, plaintext: &str) -> Result<String, Box<dyn std::error::Error>> {
        let ciphertext = self
            .cipher
            .encrypt(&self.material.nonce.into(), plaintext.as_bytes())
            .map_err(|e| format!("Encryption failed: {}", e))?;

        // Format: salt (16 bytes) + ciphertext
        let mut combined = Vec::new();
        combined.extend_from_slice(&self.material.salt);
        combined.extend_from_slice(&ciphertext);

        Ok(hex::encode(combined))
    }

    fn decrypt(&self, encrypted_hex: &str) -> Result<String, Box<dyn std::error::Error>> {
        let combined = hex::decode(encrypted_hex)?;

        if combined.len() < 16 {
            return Err("Invalid encrypted data".into());
        }

        let ciphertext = &combined[16..];

        let plaintext = self
            .cipher
            .decrypt(&self.material.nonce.into(), ciphertext)
            .map_err(|e| format!("Decryption failed: {}", e))?;

        Ok(String::from_utf8(plaintext)?)
    }
}

fn get_password(is_decrypt: bool) -> Result<String, Box<dyn std::error::Error>> {
    if is_decrypt {
        Ok(Password::new().with_prompt("Password").interact()?)
    } else {
        Ok(Password::new()
            .with_prompt("New Password")
            .with_confirmation("Confirm password", "Passwords mismatching")
            .interact()?)
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let cli = Cli::parse();
    let is_decrypt = matches!(cli.command, Some(Commands::Decrypt));

    let mut password = get_password(is_decrypt)?;
    let encryptor = Encryptor::new(&password);
    password.zeroize(); // Clear password from memory

    if is_decrypt {
        let input: String = Input::new()
            .with_prompt("Enter encrypted data")
            .interact_text()?;

        let decrypted = encryptor.decrypt(&input)?;
        println!("Decrypted: {}", decrypted);
    } else {
        let input: String = Input::new()
            .with_prompt("Enter text to encrypt")
            .interact_text()?;

        let encrypted = encryptor.encrypt(&input)?;
        println!("Encrypted: {}", encrypted);
    }

    Ok(())
}
