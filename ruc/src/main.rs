mod consts;
mod crypto;

use crate::consts::{ANSI_BLUE, ANSI_RESET, RUC_LOGO};
use clap::{Parser, Subcommand};





#[derive(Parser)]
#[command(name = "ruc")]
#[command(about = "A CPB tool")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Encrypt values
    Encrypt,
    /// Decrypt values
    Decrypt,
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let cli = Cli::parse();

    println!("{}{}{}", ANSI_BLUE, RUC_LOGO, ANSI_RESET);

    match cli.command {
        Commands::Encrypt => crypto::encrypt(),
        Commands::Decrypt => crypto::decrypt(),
    }
}
