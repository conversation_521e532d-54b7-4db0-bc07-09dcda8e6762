use blake2::{Blake2b512, Digest};
use chacha20poly1305::{ChaCha20Poly1305, KeyInit, aead::Aead};
use rand::RngCore;
use std::io::stdin;

pub fn read_input() -> String {
    let mut input = String::new();
    stdin().read_line(&mut input).expect("Failed to read input");
    input.trim().to_string()
}

pub fn derive_key_and_nonce(password: &str, salt: &[u8]) -> ([u8; 32], [u8; 12]) {
    let mut hasher = Blake2b512::default();
    hasher.update(password.as_bytes());
    hasher.update(salt);
    let hash = hasher.finalize();
    
    let mut key = [0u8; 32];
    let mut nonce = [0u8; 12];
    
    key.copy_from_slice(&hash[..32]);
    nonce.copy_from_slice(&hash[32..44]);
    
    (key, nonce)
}

pub fn encrypt(plaintext: &str, password: &str) -> Result<String, Box<dyn std::error::Error>> {
    let mut salt = [0u8; 16];
    rand::rng().fill_bytes(&mut salt);
    
    let (key, nonce) = derive_key_and_nonce(password, &salt);
    let cipher = ChaCha20Poly1305::new(&key.into());
    
    let ciphertext = cipher.encrypt(&nonce.into(), plaintext.as_bytes())?;
    
    // Format: salt (16 bytes) + ciphertext
    let mut combined = Vec::new();
    combined.extend_from_slice(&salt);
    combined.extend_from_slice(&ciphertext);
    
    Ok(hex::encode(combined))
}

pub fn decrypt(encrypted_hex: &str, password: &str) -> Result<String, Box<dyn std::error::Error>> {
    let combined = hex::decode(encrypted_hex)?;
    
    if combined.len() < 16 {
        return Err("Invalid encrypted data".into());
    }
    
    let salt = &combined[..16];
    let ciphertext = &combined[16..];
    
    let (key, nonce) = derive_key_and_nonce(password, salt);
    let cipher = ChaCha20Poly1305::new(&key.into());
    
    let plaintext = cipher.decrypt(&nonce.into(), ciphertext).unwrap();
    Ok(String::from_utf8(plaintext)?)
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Enter text to encrypt:");
    let input = read_input();
    
    println!("Enter password:");
    let password = read_input();
    
    // Encrypt
    let encrypted_hex = encrypt(&input, &password)?;
    println!("Encrypted: {}", encrypted_hex);
    
    // Decrypt to verify
    let decrypted = decrypt(&encrypted_hex, &password)?;
    println!("Decrypted: {}", decrypted);
    
    Ok(())
}
